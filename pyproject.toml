[build-system]
requires = ["setuptools", "wheel", "setuptools_scm==8.2.0"]
build-backend = "setuptools.build_meta"

[tool.black]
line-length = 88
target-version = ['py38']

[tool.isort]
profile = "black"
skip = ["src/llmcompressor/transformers/tracing/", "src/llmcompressor/version.py"]

[tool.mypy]
files = "src/guidellm"

[tool.ruff]
exclude = ["build", "dist", "env", ".venv", "src/llmcompressor/transformers/tracing/"]
lint.select = ["E", "F", "W"]
lint.extend-ignore = ["E203", "W605"]

# flake8 configuration is non-functional, only exists for vscode extensions
# for the true flake8 configuration, see `Makefile`
[tool.flake8]
max-line-length = 88
extend-ignore = ["E203", "W605"]

[tool.pytest.ini_options]
markers = [
    "smoke: quick tests to check basic functionality",
    "sanity: tests to ensure that new changes do not break existing functionality",
    "regression: detailed tests to ensure major functions work correctly",
    "integration: tests which integrate with a third party service such as HF",
    "unit: tests to ensure code correctness and regression test functionality",
    "example: tests for content in the 'examples' folder",
    "multi_gpu: tests that require multiple GPUs",
]
tmp_path_retention_policy = "failed"
