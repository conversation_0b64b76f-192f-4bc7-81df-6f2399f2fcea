test_stage:
  quant_modifiers:
    QuantizationModifier:
      ignore:
        - model.layers.0.mlp.down_proj
        - model.layers.1.mlp.down_proj
        - model.layers.2.mlp.down_proj
        - model.layers.3.mlp.down_proj
        - model.layers.4.mlp.down_proj
        - model.layers.5.mlp.down_proj
      config_groups:
          group_0:
              weights:
                  num_bits: 8
                  type: "int"
                  symmetric: False
                  strategy: "tensor"
              input_activations: null
              output_activations: null
              targets: ["Linear"]
  pruning_modifiers:
    ConstantPruningModifier:
      targets: [
        "re:.*self_attn.q_proj",
        "re:.*self_attn.k_proj",
        "re:.*self_attn.v_proj",
        "re:.*self_attn.o_proj",
        "re:.*mlp.gate_proj",
        "re:.*mlp.up_proj"
      ]
      start: 0
