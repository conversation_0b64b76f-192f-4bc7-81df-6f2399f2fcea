pruning_stage:
    obcq_modifiers:
        SparseGPTModifier:
            sparsity: 0.5
            mask_structure: "2:4"
            targets: ["Linear"]
            ignore: ["re:.*lm_head"]
quant_stage:
    quant_modifiers:
        QuantizationModifier:
            ignore: ["lm_head"]
            config_groups:
                group_0:
                    weights:
                        num_bits: 8
                        type: float
                        strategy: channel
                        dynamic: false
                        symmetric: true
                    input_activations:
                        num_bits: 8
                        type: float
                        strategy: token
                        dynamic: true
                        symmetric: true
                    targets: ["Linear"]