name: Build and Publish Docker Images

on:
  release:
    types: [published]

jobs:
  build-and-push-docker-image:
    name: Build and Push Version Tagged Docker Images to GitHub Container Registry
    runs-on: ubuntu-latest
    permissions:
      contents: read
      packages: write

    steps:
      - name: Set up Docker Buildx
        if: ${{ startsWith(github.ref, 'refs/tags/v') }}
        id: buildx
        uses: docker/setup-buildx-action@v2
        with:
          buildkitd-flags: --debug

      - name: Login to Github Packages
        if: ${{ startsWith(github.ref, 'refs/tags/v') }}
        uses: docker/login-action@v2
        with:
          registry: ghcr.io
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Checkout code
        if: ${{ startsWith(github.ref, 'refs/tags/v') }}
        uses: actions/checkout@v4
        with:
          fetch-depth: 1

      - name: Get Tag
        id: extract_tag
        run: echo "tag=${GITHUB_REF_NAME#*/}" >> $GITHUB_OUTPUT

      - name: Current Version Name
        if: ${{ startsWith(github.ref, 'refs/tags/v') }}
        run: |
          echo ${{ steps.extract_tag.outputs.tag }}

      - name: Build and push llmcompressor latest using default cuda 11.1.1
        if: ${{ startsWith(github.ref, 'refs/tags/v') }}
        uses: docker/build-push-action@v2
        with:
          context: ./docker
          build-args: |
            DEPS=all
            VERSION=${{ steps.extract_tag.outputs.tag }}
          push: true
          tags: |
            ghcr.io/neuralmagic/sparseml:latest

      - name: Build and push llmcompressor with all dependencies and default cuda 11.1.1
        if: ${{ startsWith(github.ref, 'refs/tags/v') }}
        uses: docker/build-push-action@v2
        with:
          context: ./docker
          build-args: |
            DEPS=all
            VERSION=${{ steps.extract_tag.outputs.tag }}
          push: true
          tags: |
            ghcr.io/neuralmagic/sparseml:${{ steps.extract_tag.outputs.tag }}

      - name: Build and push llmcompressor with all dependencies and cuda 10.2
        if: ${{ startsWith(github.ref, 'refs/tags/v') }}
        uses: docker/build-push-action@v2
        with:
          context: ./docker
          build-args: |
            DEPS=all
            VERSION=${{ steps.extract_tag.outputs.tag }}
            CUDA=10.2
          push: true
          tags: |
            ghcr.io/neuralmagic/sparseml:${{ steps.extract_tag.outputs.tag }}-cu102

      - name: Build and push llmcompressor-base cu11.1.1
        if: ${{ startsWith(github.ref, 'refs/tags/v') }}
        uses: docker/build-push-action@v2
        with:
          context: ./docker
          build-args: |
            DEPS=base
            VERSION=${{ steps.extract_tag.outputs.tag }}
          push: true
          tags: |
            ghcr.io/neuralmagic/sparseml-base:${{ steps.extract_tag.outputs.tag }}

      - name: Build and push llmcompressor-base cu10.2
        if: ${{ startsWith(github.ref, 'refs/tags/v') }}
        uses: docker/build-push-action@v2
        with:
          context: ./docker
          build-args: |
            DEPS=base
            VERSION=${{ steps.extract_tag.outputs.tag }}
            CUDA=10.2
          push: true
          tags: |
            ghcr.io/neuralmagic/sparseml-base:${{ steps.extract_tag.outputs.tag }}-cu10.2


      - name: Image digest
        if: ${{ startsWith(github.ref, 'refs/tags/v') }}
        run: echo ${{ steps.docker_build.outputs.digest }}
