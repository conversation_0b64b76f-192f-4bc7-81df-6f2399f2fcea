name: Check Markdown links

on:
  push:
    branches:
      - main
  pull_request:
    branches:
      - main

  # Allows you to run this workflow manually from the Actions tab
  workflow_dispatch:

jobs:
  markdown-link-check:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4
    - uses: umbrelladocs/action-linkspector@v1
      with:
        github_token: ${{ secrets.github_token }}
        reporter: github-pr-review
        fail_on_error: true
        config_file: '.github/workflows/linkspector/linkspector.yml'
