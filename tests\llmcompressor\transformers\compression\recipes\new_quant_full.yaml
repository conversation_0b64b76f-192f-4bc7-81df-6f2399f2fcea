test_stage:
    quant_modifiers:
        GPTQModifier:
            block_size: 128
            ignore: ["lm_head", "model.layers.0.mlp.down_proj"]
            config_groups:
                group_0:
                    weights:
                        num_bits: 8
                        type: "int"
                        symmetric: false
                        strategy: "channel"
                    input_activations:
                        num_bits: 8
                        type: "int"
                        symmetric: false
                        strategy: "tensor"
                    output_activations: null
                    targets: ["Linear"]