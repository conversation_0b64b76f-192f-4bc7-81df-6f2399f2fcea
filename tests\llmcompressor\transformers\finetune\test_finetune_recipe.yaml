test_stage:
  pruning_modifiers:
    ConstantPruningModifier:
      targets: [
        "re:.*self_attn.q_proj",
        "re:.*self_attn.k_proj",
        "re:.*self_attn.v_proj",
        "re:.*self_attn.o_proj",
        "re:.*mlp.gate_proj",
        "re:.*mlp.up_proj"
      ]
      start: 0
  distillation_modifiers:
    OutputDistillationModifier:
      targets: ["re:model.layers.\\d+$"]
      comparison: "square_head"
      start: 0
      orig_scale: 1.0
      distill_scale: 1.0