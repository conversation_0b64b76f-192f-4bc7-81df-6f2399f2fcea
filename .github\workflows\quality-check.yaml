name: Quality Checks
on:
  push:
    branches:
      - main
      - 'release/*'
  pull_request:
    branches:
      - main
      - 'release/*'
jobs:
  quality-check:
    runs-on: ubuntu-22.04
    steps:
      - uses: actions/setup-python@v5
        with:
          python-version: '3.9'
      - uses: actions/checkout@v4
      - name: "⚙️ Install dependencies"
        run: pip3 install .[dev]
      - name: "🧹 Running quality checks"
        run: make quality
