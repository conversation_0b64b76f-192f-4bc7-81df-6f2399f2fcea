initial_sparsity_stage:
  run_type: oneshot
  obcq_modifiers:
    SparseGPTModifier:
      sparsity: 0.5
      block_size: 128
      percdamp: 0.01
      mask_structure: "0:0"
      targets: ["Linear"]
      ignore: ["re:.*lm_head"]
initial_training_stage:
  run_type: train
  pruning_modifiers:
    ConstantPruningModifier:
      targets: '__ALL__'
      start: 0
next_sparsity_stage:
  run_type: oneshot
  obcq_modifiers:
    SparseGPTModifier:
      sparsity: 0.7
      block_size: 128
      percdamp: 0.01
      mask_structure: "0:0"
      targets: ["Linear"]
      ignore: ["re:.*lm_head"]
next_training_stage:
  run_type: train
  pruning_modifiers:
    ConstantPruningModifier:
      targets: '__ALL__'
      start: 0