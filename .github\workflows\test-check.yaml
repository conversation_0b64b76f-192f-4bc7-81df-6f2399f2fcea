name: Test Checks (Base/PyTorch)
on:
  pull_request:
    branches:
      - main
  push:

env:
  CADENCE: "commit"

jobs:

  base-tests:
    runs-on: ubuntu-22.04
    steps:
      - uses: actions/setup-python@v5
        with:
          python-version: '3.12'
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0
          fetch-tags: true
      - name: "⚙️ Install dependencies"
        run: pip3 install -U pip setuptools && pip3 install .[dev]
      - uses: actions/checkout@v4
        with:
          repository: "neuralmagic/compressed-tensors"
          path: "compressed-tensors"
          fetch-depth: 0
          fetch-tags: true
      - name: "⚙️ Install compressed-tensors dependencies"
        run: |
          pip3 uninstall -y compressed-tensors
          export GIT_CEILING_DIRECTORIES="$(pwd)"
          cd compressed-tensors
          BUILD_TYPE=nightly pip3 install .
      - name: "Clean compressed-tensors directory"
        run: rm -r compressed-tensors/
      - name: "🔬 Running base tests"
        run: make test

  pytorch-tests:
    runs-on: ubuntu-22.04
    steps:
      - uses: actions/setup-python@v5
        with:
          python-version: '3.11'
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0
          fetch-tags: true
      - name: "⚙️ Install dependencies"
        run: pip3 install -U pip setuptools && pip3 install .[dev]
      - uses: actions/checkout@v4
        with:
          repository: "neuralmagic/compressed-tensors"
          path: "compressed-tensors"
          fetch-depth: 0
          fetch-tags: true
      - name: "⚙️ Install compressed-tensors dependencies"
        run: |
          pip3 uninstall -y compressed-tensors
          export GIT_CEILING_DIRECTORIES="$(pwd)"
          cd compressed-tensors
          BUILD_TYPE=nightly pip3 install .
      - name: "Clean compressed-tensors directory"
        run: rm -r compressed-tensors/
      - name: "🔬 Running pytorch tests"
        run: |
          pytest -v tests/llmcompressor/pytorch

  compat-pytorch-1_9-pytorch-tests:
    runs-on: ubuntu-22.04
    steps:
      - uses: actions/setup-python@v5
        with:
          python-version: '3.10'
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0
          fetch-tags: true
      - name: "⚙️ Install dependencies"
        run: pip3 install -U pip setuptools && pip3 install .[dev]
      - uses: actions/checkout@v4
        with:
          repository: "neuralmagic/compressed-tensors"
          path: "compressed-tensors"
          fetch-depth: 0
          fetch-tags: true
      - name: "⚙️ Install compressed-tensors dependencies"
        run: |
          pip3 uninstall -y compressed-tensors
          export GIT_CEILING_DIRECTORIES="$(pwd)"
          cd compressed-tensors
          BUILD_TYPE=nightly pip3 install .
      - name: "Clean compressed-tensors directory"
        run: rm -r compressed-tensors/
      - name: "🔬 Running pytorch tests"
        run: |
          pytest -v tests/llmcompressor/pytorch
