test_stage:
    quant_modifiers:
        QuantizationModifier:
            ignore: ["lm_head", "model.layers.0.mlp.down_proj"]
            config_groups:
                group_0:
                    weights:
                        num_bits: 8
                        type: "int"
                        symmetric: true
                        strategy: "tensor"
                    input_activations: null
                    output_activations: null
                    targets: ["Linear", "Embedding"]
        GPTQModifier:
            block_size: 128
            targets: ["re:model.layers.\\d+$"]