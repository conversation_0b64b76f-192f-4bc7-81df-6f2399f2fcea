test_oneshot_stage:
  obcq_modifiers:
    SparseGPTModifier:
      sparsity: 0.7
      block_size: 128
      percdamp: 0.01
      mask_structure: "0:0"
      targets: ["Linear"]
      ignore: ["re:.*lm_head"]
test_train_stage:
  pruning_modifiers:
    ConstantPruningModifier:
      targets: [
        "re:.*self_attn.q_proj",
        "re:.*self_attn.k_proj",
        "re:.*self_attn.v_proj",
        "re:.*self_attn.o_proj",
        "re:.*mlp.down_proj",
        "re:.*mlp.gate_proj",
        "re:.*mlp.up_proj"
      ]
      start: 0