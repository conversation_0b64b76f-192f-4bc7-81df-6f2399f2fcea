test_stage:
    quant_modifiers:
        QuantizationModifier:
            ignore: ["lm_head", "model.layers.0.mlp.down_proj"]
            config_groups:
                group_0:
                    weights:
                        num_bits: 4
                        type: "int"
                        symmetric: False
                        strategy: "channel"
                    input_activations: null
                    output_activations: null
                    targets: ["Linear"]
        GPTQModifier:
            block_size: 128